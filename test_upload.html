<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文件上传</title>
</head>
<body>
    <h2>测试文件上传功能</h2>
    <form id="uploadForm" enctype="multipart/form-data">
        <div>
            <label for="file">选择图片文件:</label>
            <input type="file" id="file" name="file" accept="image/*" required>
        </div>
        <br>
        <button type="submit">上传</button>
    </form>
    
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('file');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch('/file/upload', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Authorization': 'Bearer YOUR_TOKEN_HERE' // 需要替换为实际的token
                    }
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>上传结果:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
                if (result.code === 200) {
                    console.log('文件ID:', result.data.id);
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>上传失败:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
