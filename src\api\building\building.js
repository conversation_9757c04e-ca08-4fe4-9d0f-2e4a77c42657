import request from '@/utils/request'
// 查询楼宇信息列表
export function listBuilding(query) {
  return request({
    url: '/building/building/list',
    method: 'get',
    params: query
  })
}

// 查询楼宇信息详细
export function getBuilding(id) {
  return request({
    url: '/building/building/' + id,
    method: 'get'
  })
}

// 新增楼宇信息
export function addBuilding(data) {
  return request({
    url: '/building/building/add',
    method: 'post',
    data: data
  })
}

// 修改楼宇信息
export function updateBuilding(data) {
  return request({
    url: '/building/building/edit',
    method: 'post',
    data: data
  })
}

// 删除楼宇信息
export function delBuilding(id) {
  return request({
    url: '/building/building/' + id,
    method: 'delete'
  })
}

// // 楼宇全景图上传接口
// export function uploadBuildingPanorama(data) {
//   return request({
//     url: '/file/upload',
//     method: 'post',
//     data: data
//   })
// }