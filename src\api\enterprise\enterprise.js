import request from '@/utils/request'
// 查询企业信息列表
export function listEnterprise(query) {
  return request({
    url: '/enterprise/enterprise/list',
    method: 'get',
    params: query
  })
}

// 查询企业信息详细
export function getEnterprise(id) {
  return request({
    url: '/enterprise/enterprise/' + id,
    method: 'get'
  })
}

// 新增企业信息
export function addEnterprise(data) {
  return request({
    url: '/enterprise/enterprise/add',
    method: 'post',
    data: data
  })
}

// 修改企业信息
export function updateEnterprise(data) {
  return request({
    url: '/enterprise/enterprise/edit',
    method: 'post',
    data: data
  })
}

// 删除企业信息
export function delEnterprise(id) {
  return request({
    url: '/enterprise/enterprise/' + id,
    method: 'delete'
  })
}

// 写入此次导入数据
export function writeDataEnterprise() {
   return request({
    url: '/enterprise/enterprise/writeData',
    method: 'post',
  })
}
